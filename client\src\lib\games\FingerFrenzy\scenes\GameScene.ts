import * as Phaser from 'phaser';
import ScoreManager from '../../managers/ScoreManager';
import LivesManager from '../../managers/LivesManager';
import GridBlock from '../objects/GridBlock';
import GameConfig from '../config/GameConfig';
import TicTapsConnector from '../utils/TicTapsConnector';
import { gameActions, gameState } from '$lib/stores';
import type { SocketClient } from '$lib/socket';
import type { FingerFrenzyConfig } from '../index';

export default class GameScene extends Phaser.Scene {
  private blocks: GridBlock[] = [];

  private gameEnd: boolean = false;

  // UI Elements
  private gridContainer!: Phaser.GameObjects.Container;

  private scoreManager!: ScoreManager;
  private livesManager!: LivesManager;

  // TicTaps integration
  private ticTaps: TicTapsConnector;
  private socketClient: SocketClient | null = null;

  // Game session data
  private roomId: string = 'room-' + Date.now().toString(36);
  private gameId: string = 'finger-frenzy';

  constructor() {
    super({ key: 'GameScene' });
    this.ticTaps = new TicTapsConnector();
  }

  init(): void {
    // Reset game state
    this.gameEnd = false;

    // Get socket client from game registry
    const gameConfig = this.registry.get('gameConfig') as FingerFrenzyConfig;
    this.socketClient = gameConfig?.socketClient || null;
    this.roomId = gameConfig?.roomId || 'room-' + Date.now().toString(36);
    this.gameId = gameConfig?.gameId || 'finger-frenzy';

    // Setup socket event listeners
    this.setupSocketEventListeners();

    // Initialize managers
    this.scoreManager = new ScoreManager(this, {
      initialScore: 0,
      fontSize: '80px',
      scoreColor: '#33DDFF'
    });
    this.livesManager = new LivesManager(this);

  }

  create(): void {
    // Simple gradient background
    this.createBackground();
    
    // Create game UI and start
    this.createGrid();
    gameActions.startGame();

    // Start countdown immediately
    this.startCountdown();
  }

  shutdown(): void {
    this.tweens.killAll();

    // Clean up socket event listeners
    this.cleanupSocketEventListeners();

    // Clean up managers
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }
    if (this.livesManager) {
      this.livesManager.destroy();
    }
  }

  /**
   * Clean up socket event listeners
   */
  private cleanupSocketEventListeners(): void {
    if (!this.socketClient) return;

    this.socketClient.removeCustomEventListener('started', () => {});
    this.socketClient.removeCustomEventListener('action_result', () => {});
    this.socketClient.removeCustomEventListener('game_error', () => {});
    console.log('Socket event listeners cleaned up for FingerFrenzy GameScene');
  }

  /**
   * Setup socket event listeners for server communication
   */
  private setupSocketEventListeners(): void {
    if (!this.socketClient) return;

    // Add custom event listeners for game-specific handling
    this.socketClient.addCustomEventListener('started', (data: any) => {
      console.log('Game started by server:', data);

      this.startGame();
    });

    this.socketClient.addCustomEventListener('action_result', (data: any) => {
      console.log('Action result from server:', data);
      if (data.actionType === 'tile_tap') {
        this.handleTileTapResult(data.data);
      }
    });

    this.socketClient.addCustomEventListener('game_error', (data: any) => {
      console.error('Game error from server:', data);
    });

    console.log('Socket event listeners setup for FingerFrenzy GameScene');
  }

  private createBackground(): void {
    const { width, height } = this.cameras.main;
    
    // Create gradient background
    const bgTexture = this.textures.createCanvas('bgTexture', width, height);
    const bgContext = bgTexture?.getContext();
    
    if (bgContext && bgTexture) {
      const gradient = bgContext.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#212429');
      gradient.addColorStop(1, '#1C1D22');
      
      bgContext.fillStyle = gradient;
      bgContext.fillRect(0, 0, width, height);
      bgTexture.refresh();
      
      this.add.image(width / 2, height / 2, 'bgTexture').setOrigin(0.5);
    } else {
      // Fallback solid color
      this.cameras.main.setBackgroundColor('#1C1D22');
    }
  }

  private async startCountdown(): Promise<void> {
    for (let i = 0; i < 4; i++) {
      // Play sound
      try {
        this.sound.play(i === 3 ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      await new Promise<void>((resolve) => {
        this.time.delayedCall(1300, () => resolve());
      });
    }

    // Send game start event to server using proper format
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.startGame();
    }
  }

  private createGrid(): void {
    const { width, height } = this.cameras.main;
    
    // Container setup
    const containerWidth = Math.min(width * 0.9, 550);
    const containerHeight = containerWidth * 1.2; //1.3
    
    this.gridContainer = this.add.container(width / 2, height * 0.6);
    
    // Background
    const bg = this.add.graphics();
    bg.fillStyle(0x1a1a1a, 1);
    bg.fillRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    bg.lineStyle(4, 0x4579F5, 1);
    bg.strokeRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    this.gridContainer.add(bg);

    // Create 4x4 grid
    this.blocks = [];
    const gap = containerWidth * 0.03;
    const cellWidth = (containerWidth - (gap * 5)) / 4;
    const cellHeight = (containerHeight - (gap * 5)) / 4;
    const startX = -containerWidth / 2 + gap;
    const startY = -containerHeight / 2 + gap;

    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        const x = startX + col * (cellWidth + gap) + cellWidth / 2;
        const y = startY + row * (cellHeight + gap) + cellHeight / 2;
        
        const block = new GridBlock(this, x, y, cellWidth, cellHeight, row, col);
        this.blocks.push(block);
        this.gridContainer.add(block);
        
        block.on('pointerdown', (pointer: Phaser.Input.Pointer) => this.onBlockClick(block, pointer));
      }
    }
  }

  private onBlockClick(block: GridBlock, pointer: Phaser.Input.Pointer): void {
    if (this.gameEnd || !block) return;
    
    // Check if this is a fresh click (not a held touch)
    // pointer.getDuration() returns how long the pointer has been down
    if (pointer.getDuration() > 100) {
      return; // Ignore held touches
    }
    
    // Disable the block's input immediately to prevent same-touch duplicate events
    block.disableInteractive();

    // Calculate points based on reaction time
    const currentTime = Date.now();
    const reactionTime = currentTime - block.getActivationTime();
    console.log('reactionTime', reactionTime);

    // Send tile tap event to server using proper format
    if (this.socketClient && this.socketClient.isConnected()) {
      const tileId = this.getTileId(block);
      this.socketClient.sendTileTap(tileId, reactionTime);
    }

  }

  private activateRandomBlock(excludeBlockIndex?: number): void {
    if (this.gameEnd) return;
    
    const currentActiveCount = this.blocks.filter(b => b.getBlockActive()).length;
    if (currentActiveCount >= GameConfig.INITIAL_ACTIVE_BLOCKS) return;

    // Get list of available positions (exclude currently active blocks and the clicked block)
    const availablePositions: number[] = [];
    
    this.blocks.forEach((block, index) => {
      const isActive = block.getBlockActive();
      const isExcluded = excludeBlockIndex !== undefined && index === excludeBlockIndex;
      
      if (!isActive && !isExcluded) {
        availablePositions.push(index);
      }
    });
    
    // If we have available positions, activate one randomly
    if (availablePositions.length > 0) {
      const randomIndex = Phaser.Utils.Array.GetRandom(availablePositions);
      this.blocks[randomIndex].setBlockActive(true);
    }
  }

  private startGame(): void {
    // Reset all blocks
    this.blocks.forEach(block => block.reset());

    // Activate initial blocks
    const positions = [
      { row: 1, col: 2 },
      { row: 2, col: 3 },
      { row: 0, col: 1 }
    ];

    for (const pos of positions) {
      const index = pos.row * 4 + pos.col;
      if (index < this.blocks.length) {
        this.blocks[index].setBlockActive(true);
      }
    }


    console.log('Game started with 3 active blocks');
  }

  private endGame(): void {
    if (this.gameEnd) return;

    this.gameEnd = true;
    gameActions.endGame();

    this.sound.play('timeout');

    // Immediately disable all blocks to prevent further interaction
    this.blocks.forEach(block => block.disableInteractive());
  }

  quitGame(): void {
    this.endGame();

    // Notify server of game end
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.endGame('manual');
    }
  }

  /**
   * Get tile ID for a block based on its position in the grid
   */
  private getTileId(block: GridBlock): string {
    const blockIndex = this.blocks.indexOf(block);
    const row = Math.floor(blockIndex / 4);
    const col = blockIndex % 4;
    return `block_${row}_${col}`;
  }

  /**
   * Handle tile tap result from server
   */
  private handleTileTapResult(data: any): void {
    console.log('Handling tile tap result:', data);

    const block = this.blocks.find(b => b.getTileId() === data.tileId);
    if (!block) {
      console.warn('Block not found for tile ID:', data.tileId);
      return;
    }
    console.log('Block found:', block.getTileId());

    if (data.isCorrect) {
      // Active block clicked - add points
      this.sound.play('right');
      block.setBlockActive(false);

      this.scoreManager.addPoints(data.points, {
        startX: this.gridContainer.x + block.x ,
        startY: this.gridContainer.y + block.y,
        color: '#ffff00',
        points: data.points
      });
    } else {
      // Inactive block clicked - lose points
      this.sound.play('wrong');
      block.setBlockWrong();

      gameActions.updateLives(data.newLives);

      this.scoreManager.subtractPoints(data.points, {
        startX: this.gridContainer.x + block.x,
        startY: this.gridContainer.y + block.y,
        color: '#ff0000',
        points: data.points
      });

      this.livesManager.deductHeart(
        this.gridContainer.x + block.x,
        this.gridContainer.y + block.y,
        this.gridContainer,
      );
    }

    // Re-enable interaction after a short delay to allow the touch to complete
    this.time.delayedCall(GameConfig.COOLDOWN_DURATION * 1000, () => {
      block.setInteractive({ useHandCursor: true });
    });

    // Update local game state based on server response
    if (data.gameState) {
      // this.scoreManager.setScore(data.gameState.score);

      gameActions.updateScore(data.gameState.score);
      // Note: LivesManager doesn't have setLives, we'll handle lives through deduction events
    }

    // Update grid state if provided
    if (data.gridState) {
      this.syncGridState(data.gridState);
    }

    // Handle game end
    if (data.gameEnded) {
      this.endGame();
    }
  }

  /**
   * Sync grid state with server
   */
  private syncGridState(gridState: any): void {
    if (!gridState || !gridState.blocks) return;

    gridState.blocks.forEach((serverBlock: any) => {
      const blockIndex = serverBlock.index;
      if (blockIndex >= 0 && blockIndex < this.blocks.length) {
        this.blocks[blockIndex].setBlockActive(serverBlock.isActive);
      }
    });
  }

}
