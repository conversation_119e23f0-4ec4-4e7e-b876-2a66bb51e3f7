import { gameState, gameActions } from '../stores';
// import { PUBLIC_SOCKET_SERVER_URL } from '$env/static/public';
import { env } from '$env/dynamic/public';
import io from 'socket.io-client';
import { get } from 'svelte/store';

export class SocketClient {
	private socket: any = null;
	private serverUrl: string;

	constructor() {
		this.serverUrl = env.PUBLIC_SOCKET_SERVER_URL;
		console.log(this.serverUrl)
	}

	// Helper method to get current game state
	private getCurrentGameState() {
		return get(gameState);
	}

	async connect(authToken: string, callbacks?: any): Promise<void> {
		return new Promise((resolve, reject) => {
			this.socket = io(this.serverUrl, {
				auth: {
					token: authToken
				}
			});

			this.socket.on('connect', () => {
				console.log('Connected to game server');
				this.setupEventListeners(callbacks);

				resolve();
			});

			this.socket.on('connect_error', (error: any) => {
				console.error('Connection error:', error);
				reject(error);
			});
		});
	}

	private setupEventListeners(callbacks?: any) {
		if (!this.socket) return;

		this.socket.on('gameUpdate', (data: any) => {
			// Handle game state updates from server
			console.log('Game update received:', data);
		});

		this.socket.on('opponentScore', (score: number) => {
			gameActions.setOpponentScore(score);
		});

		this.socket.on('roomJoined', (roomData: any) => {
			console.log('Joined room:', roomData);
		});

		this.socket.on('playerJoined', (playerData: any) => {
			console.log('Player joined:', playerData);
		});

		this.socket.on('playerLeft', (playerData: any) => {
			console.log('Player left:', playerData);
		});

		this.socket.on('gameStarted', () => {
			gameActions.startGame();
		});

		this.socket.on('gameEnded', (results: any) => {
			gameActions.endGame();
			console.log('Game ended:', results);
		});

		// Generic game event listeners
		this.socket.on('initialized', (data: any) => {
			console.log('Game initialized:', data);
			// Data includes: gameState, gridState, message
			// Game is initialized but not yet started
			gameActions.initGame();
		});

		this.socket.on('started', (data: any) => {
			console.log('Game started:', data);
			// Data includes: gameId, gameState, gridState, message
			gameActions.startGame();
		});

		this.socket.on('ended', (data: any) => {
			console.log('Game ended:', data);
			// Data includes: gameId, reason, finalScore, playerId

			callbacks.onGameComplete(data.finalScore);
		});

		this.socket.on('action_result', (data: any) => {
			console.log('Game action result:', data);
			// Data includes: gameId, actionType, playerId, data

			// Handle different callbacks based on actionType
			if (data.actionType === 'tile_tap') {
				console.log('Tile tap result:', data.data);
				callbacks.onScoreUpdate(data.data.newScore);
			}
			if (data.actionType === 'cell_mark') {
				console.log('Cell mark result:', data.data);
				callbacks.onScoreUpdate(data.data.newScore);
			}
		});

		this.socket.on('score', (data: any) => {
			console.log('Score update from server:', data);

			gameActions.updateScore(data.score);
		});

		this.socket.on('timer_tick', (data: any) => {
			console.log('Timer update from server:', data);

			gameActions.updateTime(data.duration);
		});

		this.socket.on('error', (data: any) => {
			console.error('Game error:', data);
			// Data includes: gameId, message
		});
	}

	joinRoom(roomId: string) {
		if (this.socket) {
			this.socket.emit('joinRoom', { roomId });
		}
	}

	createRoom(gameId: string) {
		if (this.socket) {
			this.socket.emit('createRoom', { gameId });
		}
	}

	sendScore(score: number) {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';
			const submitScoreId = currentState.submitScoreId;

			console.log('Submitting score with state data:', {
				score,
				gameId,
				roomId,
				submitScoreId: submitScoreId ? 'present' : 'missing'
			});

			this.socket.emit('submit_score', {
				score,
				gameId,
				roomId,
				submitScoreId,
				playerId: 'player-1', // TODO: Get actual player ID
			});
		}
	}

	sendGameEvent(eventType: string, data: any) {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';

			this.socket.emit('game_action', {
				action: eventType,
				gameData: data,
				gameId,
				roomId,
				playerId: 'player-1' // TODO: Get actual player ID
			});
		}
	}

	// Generic game methods
	initGame() {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			console.log('Initializing game with state:', currentState);

			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';
			const submitScoreId = currentState.submitScoreId;

			console.log('Initializing game with state data:', {
				gameId,
				roomId,
				submitScoreId: submitScoreId ? 'present' : 'missing'
			});

			this.socket.emit('init', {
				gameId,
				roomId,
				submitScoreId
			});
		}
	}

	startGame() {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			console.log('Starting game with state:', currentState);

			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';

			console.log('Starting game with state data:', {
				gameId,
				roomId
			});

			this.socket.emit('start', {
				gameId,
				roomId
			});
		}
	}

	endGame(reason: string = 'manual') {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';
			const submitScoreId = currentState.submitScoreId;

			console.log('Ending game with state data:', {
				gameId,
				roomId,
				submitScoreId: submitScoreId ? 'present' : 'missing',
				reason
			});

			this.socket.emit('end', {
				gameId,
				roomId,
				submitScoreId,
				reason
			});
		}
	}

	sendGameAction(actionType: string, actionData: any) {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';

			this.socket.emit('action', {
				gameId,
				roomId,
				action: {
					type: actionType,
					data: actionData
				}
			});
		}
	}

	// Convenience methods for specific actions
	sendTileTap(tileId: string, reactionTime?: number) {
		this.sendGameAction('tile_tap', {
			tileId,
			reactionTime: reactionTime || 0,
			clickTime: Date.now()
		});
	}

	sendCardSelect(cardId: string, reactionTime?: number) {
		this.sendGameAction('card_select', {
			cardId,
			reactionTime: reactionTime || 0,
			clickTime: Date.now()
		});
	}

	sendNumberSelect(numberValue: number, reactionTime?: number) {
		this.sendGameAction('number_select', {
			numberValue,
			reactionTime: reactionTime || 0,
			clickTime: Date.now()
		});
	}

	disconnect() {
		if (this.socket) {
			this.socket.disconnect();
			this.socket = null;
		}
	}

	isConnected(): boolean {
		return this.socket?.connected ?? false;
	}

	// Add custom event listener
	addCustomEventListener(event: string, callback: (data: any) => void) {
		if (this.socket) {
			this.socket.on(event, callback);
		}
	}

	// Remove custom event listener
	removeCustomEventListener(event: string, callback: (data: any) => void) {
		if (this.socket) {
			this.socket.off(event, callback);
		}
	}
}

// Singleton instance
export const socketClient = new SocketClient();
