# JWT Token Parameter Usage

The TicTaps Games client now accepts a `token` URL parameter containing a JSON Web Token (JWT) with game session data.

## Token Structure

The JWT payload should contain the following fields:

```json
{
  "gameId": "finger-frenzy",
  "roomId": "room-12345", 
  "scoreSubmitId": "score-67890",
  "authToken": "auth-token-abcdef",
  "iat": 1640995200,
  "exp": 1640998800
}
```

### Required Fields

- **gameId**: The game identifier (e.g., "finger-frenzy", "bingo", "matching-mayhem", "numbers")
- **roomId**: Multiplayer room identifier for joining specific game sessions
- **scoreSubmitId**: Unique identifier for score submission tracking
- **authToken**: Authentication token for the game server connection

### Optional Fields

- **iat**: Issued at timestamp (Unix timestamp)
- **exp**: Expiration timestamp (Unix timestamp)

## Usage Examples

### Basic URL with Token

```
http://localhost:5173/game/finger-frenzy?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJnYW1lSWQiOiJmaW5nZXItZnJlbnp5Iiwicm9vbUlkIjoicm9vbS0xMjM0NSIsInNjb3JlU3VibWl0SWQiOiJzY29yZS02Nzg5MCIsImF1dGhUb2tlbiI6ImF1dGgtdG9rZW4tYWJjZGVmIiwiaWF0IjoxNjQwOTk1MjAwLCJleHAiOjE2NDA5OTg4MDB9.test-signature
```

### Testing Tool

Use the included `test-jwt-token.html` file to generate test tokens:

1. Open `client/test-jwt-token.html` in your browser
2. Fill in the game session details
3. Click "Generate JWT Token"
4. Use the generated URL to test the game page

## Implementation Details

### Token Decoding

The client automatically:

1. Extracts the `token` parameter from the URL
2. Decodes the JWT payload (base64 decode)
3. Validates the token structure
4. Updates the game state with the token data
5. Uses the auth token for socket connection

### Game State Integration

When a valid token is provided, the client:

- Sets the room ID for multiplayer functionality
- Stores the auth token for server authentication  
- Saves the score submit ID for score tracking
- Validates that the token's game ID matches the URL

### Error Handling

The client handles various error scenarios:

- **Invalid JWT format**: Logs error and continues with default values
- **Missing token**: Runs in development mode with test values
- **Game ID mismatch**: Warns about inconsistency but continues
- **Corrupted token data**: Logs detailed error information

### Development Mode

When no token is provided, the client falls back to:

- Auth token: "test-token"
- No room ID or score submit ID
- Standard development logging

## Security Considerations

- The client only decodes the JWT payload, it doesn't verify the signature
- Token validation should be handled by the game server
- Sensitive data should not be included in the JWT payload
- Use HTTPS in production to protect token transmission

## Browser Console Output

When a token is provided, you'll see console output like:

```
Game ID: finger-frenzy
Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Decoded token data: {
  gameId: "finger-frenzy",
  roomId: "room-12345", 
  scoreSubmitId: "score-67890",
  hasAuthToken: true
}
```

## Integration with PWA

This token parameter is designed to work with the TicTaps PWA architecture:

```
PWA --> iframe postMessage --> GameApp (with token parameter)
GameApp --> Socket.IO --> Node.js Game Server
```

The PWA can generate the JWT token and pass it via the iframe URL to provide seamless authentication and session management.
