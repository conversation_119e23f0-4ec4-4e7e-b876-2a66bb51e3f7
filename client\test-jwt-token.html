<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Token Test for TicTaps Games</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            word-break: break-all;
        }
        .url-link {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .url-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JWT Token Generator for TicTaps Games</h1>
        <p>This tool generates JWT tokens for testing the game page with token parameters.</p>
        
        <form id="tokenForm">
            <div class="form-group">
                <label for="gameId">Game ID:</label>
                <select id="gameId" required>
                    <option value="finger-frenzy">Finger Frenzy</option>
                    <option value="bingo">Bingo</option>
                    <option value="matching-mayhem">Matching Mayhem</option>
                    <option value="numbers">Number Sequence</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="roomId">Room ID:</label>
                <input type="text" id="roomId" value="room-12345" required>
            </div>
            
            <div class="form-group">
                <label for="scoreSubmitId">Score Submit ID:</label>
                <input type="text" id="scoreSubmitId" value="score-67890" required>
            </div>
            
            <div class="form-group">
                <label for="authToken">Auth Token:</label>
                <input type="text" id="authToken" value="auth-token-abcdef" required>
            </div>
            
            <button type="submit">Generate JWT Token</button>
            <button type="button" onclick="generateRandomValues()">Generate Random Values</button>
        </form>
        
        <div id="output" class="output" style="display: none;">
            <h3>Generated JWT Token:</h3>
            <p><strong>Token:</strong> <span id="generatedToken"></span></p>
            <p><strong>Game URL:</strong> <a id="gameUrl" class="url-link" target="_blank"></a></p>
            <h4>Decoded Payload:</h4>
            <pre id="decodedPayload"></pre>
        </div>
    </div>

    <script>
        // Simple JWT creation (for testing only - not cryptographically secure)
        function createJWT(payload) {
            const header = {
                "alg": "HS256",
                "typ": "JWT"
            };
            
            const encodedHeader = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
            const encodedPayload = btoa(JSON.stringify(payload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
            
            // For testing, we'll use a simple signature (in production, this would be properly signed)
            const signature = btoa("test-signature").replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
            
            return `${encodedHeader}.${encodedPayload}.${signature}`;
        }
        
        function generateRandomValues() {
            const gameIds = ['finger-frenzy', 'bingo', 'matching-mayhem', 'numbers'];
            const randomGameId = gameIds[Math.floor(Math.random() * gameIds.length)];
            const randomRoomId = `room-${Math.random().toString(36).substr(2, 9)}`;
            const randomScoreId = `score-${Math.random().toString(36).substr(2, 9)}`;
            const randomAuthToken = `auth-${Math.random().toString(36).substr(2, 12)}`;
            
            document.getElementById('gameId').value = randomGameId;
            document.getElementById('roomId').value = randomRoomId;
            document.getElementById('scoreSubmitId').value = randomScoreId;
            document.getElementById('authToken').value = randomAuthToken;
        }
        
        document.getElementById('tokenForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const gameId = document.getElementById('gameId').value;
            const roomId = document.getElementById('roomId').value;
            const scoreSubmitId = document.getElementById('scoreSubmitId').value;
            const authToken = document.getElementById('authToken').value;
            
            const payload = {
                gameId: gameId,
                roomId: roomId,
                scoreSubmitId: scoreSubmitId,
                authToken: authToken,
                iat: Math.floor(Date.now() / 1000),
                exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour expiration
            };
            
            const token = createJWT(payload);
            const gameUrl = `http://localhost:5173/game/${gameId}?token=${token}`;
            
            document.getElementById('generatedToken').textContent = token;
            document.getElementById('gameUrl').textContent = gameUrl;
            document.getElementById('gameUrl').href = gameUrl;
            document.getElementById('decodedPayload').textContent = JSON.stringify(payload, null, 2);
            document.getElementById('output').style.display = 'block';
        });
        
        // Generate initial random values
        generateRandomValues();
    </script>
</body>
</html>
